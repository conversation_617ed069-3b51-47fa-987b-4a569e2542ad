 <!DOCTYPE html>
    <html>
    <head>
        <title>Ring Gemini <PERSON>t</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <style>
            /* نفس كود CSS بدون تغيير */
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Tajawal:wght@400;500;700&display=swap');
            :root {
                --bg-color: #e5ddd5; --bg-image: url('https://i.pinimg.com/originals/97/c0/0e/97c00e6242483875335e21b8141663f5.jpg');
                --card-bg-color: rgba(240, 242, 245, 0.9); --header-bg-color: #f0f2f5; --footer-bg-color: #f0f2f5;
                --border-color: rgba(0, 0, 0, 0.1); --user-bubble-bg: linear-gradient(135deg, #dcf8c6, #c5eab3);
                --bot-bubble-bg: #ffffff; --text-primary: #111b21; --text-secondary: #667781;
                --accent-green: #008069; --icon-color: #54656f;
            }
            html[data-theme="dark"] {
                --bg-color: #0c141a; --bg-image: none; --card-bg-color: rgba(17, 27, 33, 0.8);
                --header-bg-color: #202c33; --footer-bg-color: #111b21; --border-color: rgba(255, 255, 255, 0.15);
                --user-bubble-bg: linear-gradient(135deg, #005c4b, #008069); --bot-bubble-bg: #202c33;
                --text-primary: #e9edef; --text-secondary: #8696a0; --accent-green: #00a884; --icon-color: #aebac1;
            }
            body { font-family: 'Inter', sans-serif; margin: 0; height: 100vh; overflow: hidden; background-color: var(--bg-color);
                   background-image: var(--bg-image); background-size: cover; background-position: center; display: flex;
                   align-items: center; justify-content: center; padding: 1em; box-sizing: border-box; transition: background-color 0.5s ease; }
            html[lang="ar"] body { font-family: 'Tajawal', sans-serif; }
            .chat-window { width: 100%; height: 100%; max-width: 450px; max-height: 95vh; display: flex; flex-direction: column;
                           background-color: var(--card-bg-color); border-radius: 16px; border: 1px solid var(--border-color);
                           backdrop-filter: blur(25px); -webkit-backdrop-filter: blur(25px); box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                           animation: fadeIn 0.5s ease-out; overflow: hidden; }
            @keyframes fadeIn { from { opacity: 0; transform: scale(0.95); } to { opacity: 1; transform: scale(1); } }
            .chat-header { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px;
                           background-color: var(--header-bg-color); flex-shrink: 0; }
            .header-title { display: flex; align-items: center; gap: 1em; }
            .header-title i { font-size: 1.5em; color: var(--accent-green); }
            .header-title h2 { margin: 0; font-size: 1.1em; font-weight: 500; color: var(--text-primary); }
            .header-controls { display: flex; gap: 1em; }
            .control-btn { background: none; border: none; font-size: 1.2em; cursor: pointer; color: var(--icon-color); }
            #chat-log { flex-grow: 1; padding: 10px 15px; overflow-y: auto; display: flex; flex-direction: column; gap: 10px; }
            .message { max-width: 75%; padding: 10px 15px; border-radius: 12px; line-height: 1.5; color: var(--text-primary);
                       animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); word-wrap: break-word; }
            @keyframes popIn { from { opacity: 0; transform: translateY(10px) scale(0.9); } to { opacity: 1; transform: translateY(0) scale(1); } }
            .user { background: var(--user-bubble-bg); align-self: flex-end; border-bottom-right-radius: 3px; }
            html[dir="rtl"] .user { align-self: flex-start; border-bottom-right-radius: 12px; border-bottom-left-radius: 3px; }
            .bot { background: var(--bot-bubble-bg); align-self: flex-start; border-bottom-left-radius: 3px; }
            html[dir="rtl"] .bot { align-self: flex-end; border-bottom-left-radius: 12px; border-bottom-right-radius: 3px; }
            .typing-indicator { align-self: flex-start; color: var(--text-secondary); font-style: italic; }
            html[dir="rtl"] .typing-indicator { align-self: flex-end; }
            #input-bar { display: flex; padding: 12px; background: var(--footer-bg-color); flex-shrink: 0; align-items: center; }
            #msg-input { flex-grow: 1; background: var(--bot-bubble-bg); border: 1px solid var(--border-color);
                         border-radius: 22px; padding: 12px 18px; font-size: 15px; color: var(--text-primary);
                         outline: none; transition: border-color 0.2s; }
            #msg-input:focus { border-color: var(--accent-green); }
            #send-btn { background: var(--accent-green); color: white; border: none; border-radius: 50%; width: 45px;
                        height: 45px; margin: 0 12px; cursor: pointer; font-size: 18px; display: flex;
                        align-items: center; justify-content: center; transition: transform 0.2s, background-color 0.2s; }
            html[dir="ltr"] #send-btn { order: 2; }
            #send-btn:hover { transform: scale(1.1); background-color: #008a6e; }

            /* أنماط النوافذ المنبثقة */
            .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%;
                     background-color: rgba(0,0,0,0.5); backdrop-filter: blur(5px); }
            .modal-content { background-color: var(--card-bg-color); margin: 10% auto; padding: 0; border-radius: 12px;
                            width: 90%; max-width: 500px; box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                            animation: modalSlideIn 0.3s ease-out; }
            @keyframes modalSlideIn { from { opacity: 0; transform: translateY(-50px); } to { opacity: 1; transform: translateY(0); } }
            .modal-header { display: flex; justify-content: space-between; align-items: center; padding: 20px;
                           border-bottom: 1px solid var(--border-color); background-color: var(--header-bg-color);
                           border-radius: 12px 12px 0 0; }
            .modal-header h3 { margin: 0; color: var(--text-primary); font-size: 1.2em; }
            .close-btn { background: none; border: none; font-size: 24px; cursor: pointer; color: var(--icon-color);
                        width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;
                        border-radius: 50%; transition: background-color 0.2s; }
            .close-btn:hover { background-color: var(--border-color); }
            .modal-body { padding: 20px; }
            .setting-group { margin-bottom: 20px; }
            .setting-group label { display: block; margin-bottom: 8px; color: var(--text-primary); font-weight: 500; }
            .setting-group input { width: 100%; padding: 12px; border: 1px solid var(--border-color);
                                  border-radius: 8px; background-color: var(--bot-bubble-bg); color: var(--text-primary);
                                  font-size: 14px; box-sizing: border-box; }
            .setting-group input:focus { outline: none; border-color: var(--accent-green); }
            .setting-group { position: relative; }
            .toggle-btn { position: absolute; right: 10px; top: 32px; background: none; border: none;
                         color: var(--icon-color); cursor: pointer; padding: 5px; }
            .modal-actions { display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px; }
            .action-btn { padding: 10px 20px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px;
                         transition: background-color 0.2s, transform 0.1s; }
            .action-btn.primary { background-color: var(--accent-green); color: white; }
            .action-btn.primary:hover { background-color: #008a6e; transform: translateY(-1px); }
            .action-btn.secondary { background-color: var(--border-color); color: var(--text-primary); }
            .action-btn.secondary:hover { background-color: var(--icon-color); }

            /* قائمة المحادثات */
            .conversations-list { max-height: 300px; overflow-y: auto; }
            .conversation-item { display: flex; justify-content: space-between; align-items: center; padding: 12px;
                               border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 8px;
                               background-color: var(--bot-bubble-bg); cursor: pointer; transition: background-color 0.2s; }
            .conversation-item:hover { background-color: var(--user-bubble-bg); }
            .conversation-name { flex-grow: 1; color: var(--text-primary); }
            .conversation-actions { display: flex; gap: 8px; }
            .conversation-actions button { background: none; border: none; color: var(--icon-color);
                                         cursor: pointer; padding: 4px; border-radius: 4px; }
            .conversation-actions button:hover { background-color: var(--border-color); }
            .delete-btn:hover { color: #ff4444 !important; }

            /* تحسينات إضافية للأزرار */
            .control-btn { transition: all 0.2s ease; }
            .control-btn:hover { background-color: var(--border-color); transform: scale(1.1); }
            .header-controls { gap: 8px; }
        </style>
    </head>
    <body>
        <div class="chat-window">
            <div class="chat-header">
                <div class="header-title"><i class="fa-solid fa-robot"></i><h2 id="ui-title"></h2></div>
                <div class="header-controls">
                    <button id="save-conversation-btn" class="control-btn" title="حفظ المحادثة"><i class="fa-solid fa-save"></i></button>
                    <button id="load-conversation-btn" class="control-btn" title="تحميل محادثة"><i class="fa-solid fa-folder-open"></i></button>
                    <button id="clear-conversation-btn" class="control-btn" title="مسح المحادثة"><i class="fa-solid fa-trash"></i></button>
                    <button id="settings-btn" class="control-btn" title="الإعدادات"><i class="fa-solid fa-cog"></i></button>
                    <button id="theme-toggle" class="control-btn"></button>
                    <button id="lang-toggle" class="control-btn"></button>
                </div>
            </div>
            <div id="chat-log"></div>
            <div id="input-bar">
                <input type="text" id="msg-input"><button id="send-btn"><i class="fa-solid fa-paper-plane"></i></button>
            </div>
        </div>

        <!-- نافذة الإعدادات -->
        <div id="settings-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="settings-title">الإعدادات</h3>
                    <button class="close-btn" onclick="closeModal('settings-modal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="api-key-input" id="api-key-label">مفتاح API:</label>
                        <input type="password" id="api-key-input" placeholder="أدخل مفتاح Gemini API">
                        <button id="toggle-api-key" class="toggle-btn"><i class="fa-solid fa-eye"></i></button>
                    </div>
                    <div class="setting-group">
                        <label for="api-url-input" id="api-url-label">عنوان API:</label>
                        <input type="text" id="api-url-input" placeholder="أدخل عنوان API">
                    </div>
                    <div class="modal-actions">
                        <button id="save-api-settings" class="action-btn primary">حفظ</button>
                        <button onclick="closeModal('settings-modal')" class="action-btn secondary">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة حفظ المحادثة -->
        <div id="save-conversation-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="save-conversation-title">حفظ المحادثة</h3>
                    <button class="close-btn" onclick="closeModal('save-conversation-modal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="conversation-name-input" id="conversation-name-label">اسم المحادثة:</label>
                        <input type="text" id="conversation-name-input" placeholder="أدخل اسم المحادثة">
                    </div>
                    <div class="modal-actions">
                        <button id="confirm-save-conversation" class="action-btn primary">حفظ</button>
                        <button onclick="closeModal('save-conversation-modal')" class="action-btn secondary">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة تحميل المحادثة -->
        <div id="load-conversation-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="load-conversation-title">تحميل محادثة</h3>
                    <button class="close-btn" onclick="closeModal('load-conversation-modal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="conversations-list" class="conversations-list">
                        <!-- قائمة المحادثات ستظهر هنا -->
                    </div>
                </div>
            </div>
        </div>

        <script>
            // === JavaScript مُحدّث للتعامل مع الحالة الجديدة ===
            let currentTheme, currentLang;
            const uiStrings = {
                en: {
                    title: "Gemini Bot",
                    placeholder: "Ask Gemini anything...",
                    typing: "Gemini is thinking...",
                    settings: "Settings",
                    apiKey: "API Key:",
                    apiUrl: "API URL:",
                    save: "Save",
                    cancel: "Cancel",
                    saveConversation: "Save Conversation",
                    loadConversation: "Load Conversation",
                    conversationName: "Conversation Name:",
                    noConversations: "No saved conversations",
                    confirmDelete: "Are you sure you want to delete this conversation?",
                    confirmClear: "Are you sure you want to clear the current conversation?"
                },
                ar: {
                    title: "بوت Gemini",
                    placeholder: "اسأل Gemini أي شيء...",
                    typing: "Gemini يفكر الآن...",
                    settings: "الإعدادات",
                    apiKey: "مفتاح API:",
                    apiUrl: "عنوان API:",
                    save: "حفظ",
                    cancel: "إلغاء",
                    saveConversation: "حفظ المحادثة",
                    loadConversation: "تحميل محادثة",
                    conversationName: "اسم المحادثة:",
                    noConversations: "لا توجد محادثات محفوظة",
                    confirmDelete: "هل أنت متأكد من حذف هذه المحادثة؟",
                    confirmClear: "هل أنت متأكد من مسح المحادثة الحالية؟"
                }
            };

            function setTheme(theme) {
                currentTheme = theme;
                document.documentElement.setAttribute('data-theme', theme);
                document.getElementById('theme-toggle').innerHTML = theme === 'dark' ? '<i class="fa-solid fa-sun"></i>' : '<i class="fa-solid fa-moon"></i>';
            }

            function setLanguage(lang) {
                currentLang = lang;
                const strings = uiStrings[lang];
                document.documentElement.lang = lang;
                document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

                // تحديث النصوص الأساسية
                document.getElementById('ui-title').textContent = strings.title;
                document.getElementById('msg-input').placeholder = strings.placeholder;
                document.getElementById('lang-toggle').textContent = lang === 'en' ? 'AR' : 'EN';

                // تحديث نصوص النوافذ المنبثقة
                const settingsTitle = document.getElementById('settings-title');
                const apiKeyLabel = document.getElementById('api-key-label');
                const apiUrlLabel = document.getElementById('api-url-label');
                const saveConversationTitle = document.getElementById('save-conversation-title');
                const conversationNameLabel = document.getElementById('conversation-name-label');
                const loadConversationTitle = document.getElementById('load-conversation-title');

                if (settingsTitle) settingsTitle.textContent = strings.settings;
                if (apiKeyLabel) apiKeyLabel.textContent = strings.apiKey;
                if (apiUrlLabel) apiUrlLabel.textContent = strings.apiUrl;
                if (saveConversationTitle) saveConversationTitle.textContent = strings.saveConversation;
                if (conversationNameLabel) conversationNameLabel.textContent = strings.conversationName;
                if (loadConversationTitle) loadConversationTitle.textContent = strings.loadConversation;

                // تحديث نصوص الأزرار
                const saveApiBtn = document.getElementById('save-api-settings');
                const confirmSaveBtn = document.getElementById('confirm-save-conversation');
                if (saveApiBtn) saveApiBtn.textContent = strings.save;
                if (confirmSaveBtn) confirmSaveBtn.textContent = strings.save;

                // تحديث placeholders
                const apiKeyInput = document.getElementById('api-key-input');
                const apiUrlInput = document.getElementById('api-url-input');
                const conversationNameInput = document.getElementById('conversation-name-input');

                if (apiKeyInput) apiKeyInput.placeholder = lang === 'ar' ? 'أدخل مفتاح Gemini API' : 'Enter Gemini API key';
                if (apiUrlInput) apiUrlInput.placeholder = lang === 'ar' ? 'أدخل عنوان API' : 'Enter API URL';
                if (conversationNameInput) conversationNameInput.placeholder = lang === 'ar' ? 'أدخل اسم المحادثة' : 'Enter conversation name';
            }

            function addMessage(text, sender) {
                const log = document.getElementById('chat-log');
                const msgDiv = document.createElement('div');
                const safeText = text.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\\n/g, '<br>');
                msgDiv.className = 'message ' + sender;
                msgDiv.innerHTML = safeText;
                log.appendChild(msgDiv);
                log.scrollTop = log.scrollHeight;
            }

            // === دوال إدارة النوافذ المنبثقة ===
            function openModal(modalId) {
                document.getElementById(modalId).style.display = 'block';
            }

            function closeModal(modalId) {
                document.getElementById(modalId).style.display = 'none';
            }

            // إغلاق النافذة عند النقر خارجها
            window.onclick = function(event) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            }

            // === دوال إدارة المحادثات ===
            async function saveCurrentConversation() {
                const conversationName = document.getElementById('conversation-name-input').value.trim();
                if (!conversationName) {
                    alert(currentLang === 'ar' ? 'يرجى إدخال اسم المحادثة' : 'Please enter conversation name');
                    return;
                }

                try {
                    const result = await window.saveConversation(conversationName);
                    alert(result);
                    closeModal('save-conversation-modal');
                    document.getElementById('conversation-name-input').value = '';
                } catch (error) {
                    alert(currentLang === 'ar' ? 'خطأ في حفظ المحادثة' : 'Error saving conversation');
                }
            }

            async function loadConversationsList() {
                try {
                    const conversations = await window.getConversationsList();
                    const listContainer = document.getElementById('conversations-list');
                    listContainer.innerHTML = '';

                    if (conversations.length === 0) {
                        listContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">' +
                            uiStrings[currentLang].noConversations + '</p>';
                        return;
                    }

                    for (let i = 0; i < conversations.length; i += 2) {
                        const fileName = conversations[i];
                        const displayName = conversations[i + 1];

                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'conversation-item';
                        itemDiv.innerHTML = `
                            <span class="conversation-name">${displayName}</span>
                            <div class="conversation-actions">
                                <button onclick="loadSelectedConversation('${fileName}')" title="${currentLang === 'ar' ? 'تحميل' : 'Load'}">
                                    <i class="fa-solid fa-download"></i>
                                </button>
                                <button class="delete-btn" onclick="deleteSelectedConversation('${fileName}')" title="${currentLang === 'ar' ? 'حذف' : 'Delete'}">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        `;
                        listContainer.appendChild(itemDiv);
                    }
                } catch (error) {
                    console.error('Error loading conversations:', error);
                }
            }

            async function loadSelectedConversation(fileName) {
                try {
                    const result = await window.loadConversation(fileName);
                    alert(result);
                    closeModal('load-conversation-modal');

                    // إعادة تحميل الصفحة لعرض المحادثة المحملة
                    location.reload();
                } catch (error) {
                    alert(currentLang === 'ar' ? 'خطأ في تحميل المحادثة' : 'Error loading conversation');
                }
            }

            async function deleteSelectedConversation(fileName) {
                if (confirm(uiStrings[currentLang].confirmDelete)) {
                    try {
                        const result = await window.deleteConversation(fileName);
                        alert(result);
                        loadConversationsList(); // إعادة تحميل القائمة
                    } catch (error) {
                        alert(currentLang === 'ar' ? 'خطأ في حذف المحادثة' : 'Error deleting conversation');
                    }
                }
            }

            async function clearCurrentConversation() {
                if (confirm(uiStrings[currentLang].confirmClear)) {
                    try {
                        const result = await window.clearCurrentConversation();
                        alert(result);
                        document.getElementById('chat-log').innerHTML = '';
                    } catch (error) {
                        alert(currentLang === 'ar' ? 'خطأ في مسح المحادثة' : 'Error clearing conversation');
                    }
                }
            }

            // === دوال إدارة الإعدادات ===
            function toggleApiKeyVisibility() {
                const input = document.getElementById('api-key-input');
                const button = document.getElementById('toggle-api-key');

                if (input.type === 'password') {
                    input.type = 'text';
                    button.innerHTML = '<i class="fa-solid fa-eye-slash"></i>';
                } else {
                    input.type = 'password';
                    button.innerHTML = '<i class="fa-solid fa-eye"></i>';
                }
            }

            async function saveApiSettings() {
                const apiKey = document.getElementById('api-key-input').value.trim();
                const apiUrl = document.getElementById('api-url-input').value.trim();

                if (!apiKey || !apiUrl) {
                    alert(currentLang === 'ar' ? 'يرجى ملء جميع الحقول' : 'Please fill all fields');
                    return;
                }

                try {
                    const result = await window.updateApiSettings(apiKey, apiUrl);
                    alert(result);
                    closeModal('settings-modal');
                } catch (error) {
                    alert(currentLang === 'ar' ? 'خطأ في حفظ الإعدادات' : 'Error saving settings');
                }
            }

            function loadApiSettings(settings) {
                if (settings.apiKey) {
                    document.getElementById('api-key-input').value = settings.apiKey;
                }
                if (settings.apiUrl) {
                    document.getElementById('api-url-input').value = settings.apiUrl;
                }
            }

            function setTyping(isTyping) {
                let indicator = document.getElementById('typing-indicator');
                if (isTyping) {
                    if (!indicator) {
                        indicator = document.createElement('div');
                        indicator.id = 'typing-indicator';
                        indicator.className = 'message bot typing-indicator';
                        indicator.innerHTML = '<i>' + uiStrings[currentLang].typing + '</i>';
                        document.getElementById('chat-log').appendChild(indicator);
                        document.getElementById('chat-log').scrollTop = document.getElementById('chat-log').scrollHeight;
                    }
                } else { if (indicator) indicator.remove(); }
            }

            async function sendMessage() {
                const input = document.getElementById('msg-input');
                const text = input.value.trim();
                if (text) {
                    addMessage(text, 'user');
                    input.value = '';
                    setTyping(true);
                    // استدعاء الدالة الجديدة
                    const botReply = await window.sendMessageToAI(text, currentLang);
                    setTyping(false);
                    addMessage(botReply, 'bot');
                }
            }
            
            // --- دالة تحميل الحالة الأولية ---
            window.onload = async () => {
                // استدعاء الدالة الجديدة للحصول على الإعدادات والسجل
                const initialState = await window.getInitialState();

                setTheme(initialState.settings.theme);
                setLanguage(initialState.settings.language);

                // تحميل إعدادات API
                loadApiSettings(initialState.settings);

                // تحميل سجل المحادثة وعرضه
                const log = document.getElementById('chat-log');
                log.innerHTML = ''; // مسح أي رسائل افتراضية
                initialState.history.forEach(item => {
                    const sender = item.role === 'user' ? 'user' : 'bot';
                    addMessage(item.parts[0].text, sender);
                });

                // === ربط الأحداث للأزرار الأساسية ===
                document.getElementById('theme-toggle').addEventListener('click', () => {
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    setTheme(newTheme);
                    window.saveSettings(newTheme, currentLang);
                });

                document.getElementById('lang-toggle').addEventListener('click', () => {
                    const newLang = currentLang === 'en' ? 'ar' : 'en';
                    setLanguage(newLang);
                    // إعادة تحميل السجل باللغة الجديدة (اختياري، أو يمكن بدء محادثة جديدة)
                    log.innerHTML = '';
                    window.saveSettings(currentTheme, newLang);
                });

                document.getElementById('send-btn').addEventListener('click', sendMessage);
                document.getElementById('msg-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') sendMessage();
                });

                // === ربط الأحداث للأزرار الجديدة ===
                document.getElementById('save-conversation-btn').addEventListener('click', () => {
                    openModal('save-conversation-modal');
                });

                document.getElementById('load-conversation-btn').addEventListener('click', () => {
                    loadConversationsList();
                    openModal('load-conversation-modal');
                });

                document.getElementById('clear-conversation-btn').addEventListener('click', clearCurrentConversation);

                document.getElementById('settings-btn').addEventListener('click', () => {
                    openModal('settings-modal');
                });

                // === ربط الأحداث للنوافذ المنبثقة ===
                document.getElementById('confirm-save-conversation').addEventListener('click', saveCurrentConversation);
                document.getElementById('save-api-settings').addEventListener('click', saveApiSettings);
                document.getElementById('toggle-api-key').addEventListener('click', toggleApiKeyVisibility);

                // السماح بحفظ المحادثة بالضغط على Enter
                document.getElementById('conversation-name-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') saveCurrentConversation();
                });
            };
        </script>
    </body>
    </html>