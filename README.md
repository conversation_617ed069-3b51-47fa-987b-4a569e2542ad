# Gemini Chat Bot - Enhanced Version

## الميزات الجديدة / New Features

### 🔧 إدارة الإعدادات / Settings Management
- **مفتاح API قابل للتخصيص**: يمكنك الآن تغيير مفتاح Gemini API من الواجهة
- **عنوان API قابل للتخصيص**: إمكانية تغيير عنوان API المستخدم
- **حفظ الإعدادات**: جميع الإعدادات تُحفظ تلقائياً

### 💾 إدارة المحادثات / Conversation Management
- **حفظ المحادثات**: احفظ محادثاتك المهمة بأسماء مخصصة
- **تحميل المحادثات**: استعد المحادثات المحفوظة في أي وقت
- **حذف المحادثات**: إدارة كاملة للمحادثات المحفوظة
- **مسح المحادثة الحالية**: ابدأ محادثة جديدة بسهولة

### 🎨 تحسينات الواجهة / UI Improvements
- **أزرار جديدة**: أزرار سهلة الاستخدام لجميع الميزات
- **نوافذ منبثقة أنيقة**: تصميم عصري للإعدادات وإدارة المحادثات
- **دعم متعدد اللغات محسن**: ترجمة كاملة لجميع العناصر الجديدة
- **أيقونات تفاعلية**: أيقونات Font Awesome لتجربة أفضل

## كيفية الاستخدام / How to Use

### تشغيل التطبيق / Running the Application
```bash
# Windows
run.bat

# أو مباشرة / Or directly
ring main.ring
```

### الأزرار الجديدة / New Buttons
1. **💾 حفظ المحادثة**: احفظ المحادثة الحالية
2. **📁 تحميل محادثة**: تصفح واستعد المحادثات المحفوظة
3. **🗑️ مسح المحادثة**: امسح المحادثة الحالية
4. **⚙️ الإعدادات**: تخصيص مفتاح API وعنوان API

### إعداد مفتاح API / API Key Setup
1. انقر على زر الإعدادات ⚙️
2. أدخل مفتاح Gemini API الخاص بك
3. (اختياري) عدّل عنوان API إذا لزم الأمر
4. انقر "حفظ"

### إدارة المحادثات / Managing Conversations
1. **للحفظ**: انقر 💾 → أدخل اسم المحادثة → احفظ
2. **للتحميل**: انقر 📁 → اختر المحادثة → تحميل
3. **للحذف**: في نافذة التحميل → انقر 🗑️ بجانب المحادثة

## الملفات والمجلدات / Files and Folders
- `main.ring`: الملف الرئيسي
- `lib.ring`: مكتبة الدوال المساعدة
- `chat_settings.json`: ملف الإعدادات
- `chat_history.ring`: سجل المحادثة الحالية
- `conversations/`: مجلد المحادثات المحفوظة

## المتطلبات / Requirements
- Ring Programming Language
- اتصال بالإنترنت
- مفتاح Gemini API صالح

## الدعم / Support
التطبيق يدعم اللغتين العربية والإنجليزية مع إمكانية التبديل السريع بينهما.
