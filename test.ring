# Test file for the enhanced chat bot

load "lib.ring"

# Test basic functions
? "Testing enhanced chat bot functions..."


? "Testing escapeJson function:"
? 'escapeJson("Hello"): ' + escapeJson("Hello")

# Test directory creation
cConversationsDir = "conversations"
if not fexists(cConversationsDir)
    system("mkdir " + cConversationsDir)
    ? "Created conversations directory"
else
    ? "Conversations directory already exists"
ok

? "All basic tests completed successfully!"
? "You can now run the main application with: ring main.ring"

# Test isalnum function
func isalnum(cChar)
    nAscii = ascii(cChar)
    return (nAscii >= 48 and nAscii <= 57) or (nAscii >= 65 and nAscii <= 90) or (nAscii >= 97 and nAscii <= 122)

? "Testing isalnum function:"
? "isalnum('a'): " + isalnum('a')
? "isalnum('1'): " + isalnum('1')
? "isalnum('@'): " + isalnum('@')

# Test escapeJson function
func escapeJson(str)
    if str = NULL { return "" }
    escaped = ""
    for i = 1 to len(str) {
        char = str[i]
        if char = '"' { escaped += '\"' 
        elseif char = "\"  escaped += '\\' 
        elseif char = '/'  escaped += '\/' 
        elseif ascii(char) = 8  escaped += '\b' 
        elseif ascii(char) = 12  escaped += '\f' 
        elseif ascii(char) = 10  escaped += '\n' 
        elseif ascii(char) = 13  escaped += '\r' 
        elseif ascii(char) = 9  escaped += '\t' 
        else  escaped += char }
    }
    return escaped
