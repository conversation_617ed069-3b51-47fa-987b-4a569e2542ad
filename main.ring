# The Main File

load "lib.ring"

# ===================================================================
# Gemini Powered Chat Bot Application using Ring and WebView
# ===================================================================


# -- المتغيرات العامة --
oWebView = NULL
cSettingsFile = "chat_settings.json"
cHistoryFile = "chat_history.ring"
cConversationsDir = "conversations"
aSettings = []
aConversationHistory = [] # ذاكرة المحادثة
//cApiKey = "AIzaSyAisNXkhbaKM3qhl-v3hxRsSf17wJDAMbU"  #"YOUR_GEMINI_API_KEY_HERE" # <--- ضع مفتاح API الخاص بك هنا
//cApiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# ===================================================================
# الدالة الرئيسية - Main Function
# ===================================================================
func main()
    aSettings = loadSettings()
    cList ="aConversationHistory =" + loadHistory()
    eval(cList) # تحميل سجل المحادثة عند بدء التشغيل
    
    oWebView = new WebView()
    oWebView {
        setTitle("Gemini Chat Bot")
        setSize(450, 650, WEBVIEW_HINT_NONE)

        # ربط الدوال الجديدة والمحسنة
        bind("getInitialState", :GetInitialState) # اسم جديد وأكثر وضوحًا
        bind("sendMessageToAI", :handleSendMessageToAI) # اسم جديد للتعامل مع الذكاء الاصطناعي
        bind("saveSettings", :handleSaveSettings)
        bind("saveConversation", :handleSaveConversation)
        bind("loadConversation", :handleLoadConversation)
        bind("getConversationsList", :handleGetConversationsList)
        bind("deleteConversation", :handleDeleteConversation)
        bind("clearCurrentConversation", :handleClearCurrentConversation)
        bind("updateApiSettings", :handleUpdateApiSettings)

      	# تحميل صفحة الاختبار
        cHtmlPath = "file://" + currentdir() + "/index.html"
        navigate(cHtmlPath)

        run()
    }
    
# ===================================================================
# معالجات WebView (الربط مع JavaScript)
# ===================================================================
# يرسل الإعدادات وسجل المحادثة عند تحميل الواجهة
func GetInitialState(id, req)
    oInitialState = [
        :settings = list2map(aSettings),
        :history = aConversationHistory
    ]
    oWebView.wreturn(id, 0, list2json(oInitialState))

# يستقبل الرسائل من الواجهة، يتصل بـ Gemini، ويرسل الرد
func handleSendMessageToAI(id, req)
    try
        aParams = json2list(req)[1]
        cUserMessage = aParams[1]
        cLang = aParams[2]
        
        # إضافة رسالة المستخدم إلى سجل المحادثة
        addToHistory(:user,  cUserMessage)

        # استدعاء Gemini API
        cBotReply = callGeminiAPI(cUserMessage, cLang, aConversationHistory)
        
        # إضافة رد الذكاء الاصطناعي إلى سجل المحادثة
        addToHistory(:model, escapeJson(cBotReply))
        
        # إرجاع الرد إلى JavaScript
        oWebView.wreturn(id, 0, '"' + escapeJson(cBotReply) + '"')
    catch
        cErrorMsg = "عذرًا، حدث خطأ أثناء الاتصال بالذكاء الاصطناعي."
        oWebView.wreturn(id, 0, '"' + escapeJson(cErrorMsg) + '"')
    done

# يحفظ الإعدادات
func handleSaveSettings(id, req)
    aReq = json2list(req)[1]
    cTheme = aReq[1]
    cLang = aReq[2]

    aSettings[1][2] = cTheme
    aSettings[2][2] = cLang

    saveSettings()
    oWebView.wreturn(id, 0, '{}')

# حفظ المحادثة الحالية
func handleSaveConversation(id, req)
    try
        aReq = json2list(req)[1]
        cConversationName = aReq[1]

        if len(aConversationHistory) = 0
            oWebView.wreturn(id, 0, '"لا توجد محادثة لحفظها"')
            return
        ok

        cResult = saveConversationToFile(cConversationName)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"خطأ في حفظ المحادثة"')
    done

# تحميل محادثة محفوظة
func handleLoadConversation(id, req)
    try
        aReq = json2list(req)[1]
        cFileName = aReq[1]

        aConversationHistory = loadConversationFromFile(cFileName)
        cResult = "تم تحميل المحادثة بنجاح"
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"خطأ في تحميل المحادثة"')
    done

# الحصول على قائمة المحادثات المحفوظة
func handleGetConversationsList(id, req)
    try
        aConversations = getConversationsList()
        oWebView.wreturn(id, 0, list2json(aConversations))
    catch
        oWebView.wreturn(id, 0, '[]')
    done

# حذف محادثة محفوظة
func handleDeleteConversation(id, req)
    try
        aReq = json2list(req)[1]
        cFileName = aReq[1]

        cResult = deleteConversationFile(cFileName)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"خطأ في حذف المحادثة"')
    done

# مسح المحادثة الحالية
func handleClearCurrentConversation(id, req)
    try
        aConversationHistory = []
        saveHistory()
        oWebView.wreturn(id, 0, '"تم مسح المحادثة"')
    catch
        oWebView.wreturn(id, 0, '"خطأ في مسح المحادثة"')
    done

# تحديث إعدادات API
func handleUpdateApiSettings(id, req)
    try
        aReq = json2list(req)[1]
        cNewApiKey = aReq[1]
        cNewApiUrl = aReq[2]

        # تحديث المتغيرات العامة
        cApiKey = cNewApiKey
        cApiUrl = cNewApiUrl

        # حفظ في الإعدادات
        updateApiSettingsInFile(cNewApiKey, cNewApiUrl)

        oWebView.wreturn(id, 0, '"تم تحديث إعدادات API بنجاح"')
    catch
        oWebView.wreturn(id, 0, '"خطأ في تحديث إعدادات API"')
    done

# ===================================================================
# منطق الذكاء الاصطناعي (الاتصال بـ Gemini)
# ===================================================================
func callGeminiAPI(cUserMessage, cLang, aHistory)
    try
        if cApiKey = "YOUR_GEMINI_API_KEY_HERE" or len(cApiKey) < 10
            if cLang = "ar" return "يرجى تعيين مفتاح Gemini API الصحيح في ملف Ring." ok
            return "Please set a valid Gemini API key in the Ring script."
        ok

        cURL = cApiUrl + "?key=" + cApiKey
        
        cSystemPrompt = "أنت مساعد ذكي ومفيد. أجب دائمًا باللغة " + iif(cLang="ar", "العربية.", "English.") +
                      " قم بتنسيق إجاباتك باستخدام ماركداون، خاصةً عند عرض الكود."
        
        aRequestContents = [[:role="user", :parts=[[:text=cSystemPrompt]]], [:role="model", :parts=[[:text="حسنًا، فهمت."]]]]
        
        nHistoryStart = max(1, len(aHistory) - 10)
        for i = nHistoryStart to len(aHistory) { aRequestContents + aHistory[i] }
        aRequestContents + [[:role="user", :parts=[[:text=cUserMessage]]]]

        oRequestData = [:contents = aRequestContents]
        cRequestJSON = list2json(oRequestData)
        
        oClient = new HTTPClient()
        oClient.setTimeout(45)
        oClient.setVerifySSL(false)
        aHeaders = ["Content-Type: application/json"]
        
        oResponse = oClient.post(cURL, cRequestJSON, aHeaders)
       
        if oResponse[:success] { return parseGeminiResponse(oResponse[:content]) }
        return "Error: " + oResponse[:status_code] + " - " + oResponse[:error]
        
    catch
        return "Exception in callGeminiAPI: " + cCatchError
    done


func parseGeminiResponse(cResponseJSON)
    try
        oResponse = json2list(cResponseJSON)
        if islist(oResponse) and oResponse[:candidates] != NULL
            return oResponse[:candidates][1][:content][:parts][1][:text]
        else
			if islist(oResponse) and oResponse[:error] != NULL
				return "Error from API: " + oResponse[:error][:message]
			ok
            return "Could not parse AI response."
        ok
    catch
        return "Error parsing JSON: " + cCatchError
    done

# ===================================================================
# إدارة الحالة (الإعدادات وسجل المحادثة)
# ===================================================================
func loadSettings()
    if fexists(cSettingsFile)
        aLoadedSettings = json2list(read(cSettingsFile))

        # تحديث المتغيرات العامة من الإعدادات المحفوظة
        for aSetting in aLoadedSettings
            if aSetting[1] = "apiKey" and len(aSetting[2]) > 10
                cApiKey = aSetting[2]
            ok
            if aSetting[1] = "apiUrl" and len(aSetting[2]) > 10
                cApiUrl = aSetting[2]
            ok
        next

        return aLoadedSettings
    ok
    return [[:theme, "dark"], [:language, "en"], [:apiKey, cApiKey], [:apiUrl, cApiUrl]]

func saveSettings()
    write(cSettingsFile, list2json(aSettings))

func loadHistory()
    cHistoryFile = cHistoryFile
    if fexists(cHistoryFile) { 
		try
			return read(cHistoryFile)
		catch
			return "[]"
		done
	}
    return "[]"

func saveHistory()
    write(cHistoryFile, list2code(aConversationHistory))

func addToHistory(cRole, cContent)
    aConversationHistory + [:role = cRole, :parts = [[:text = cContent]]]
    // إبقاء السجل بحجم معقول
    if len(aConversationHistory) > 50 { del(aConversationHistory, 1) }
    saveHistory() // حفظ السجل بعد كل رسالة

# ===================================================================
# دوال إدارة المحادثات المحفوظة
# ===================================================================

# إنشاء مجلد المحادثات إذا لم يكن موجوداً
func createConversationsDir()
    if not fexists(cConversationsDir)
        system("mkdir " + cConversationsDir)
    ok

# حفظ المحادثة الحالية في ملف
func saveConversationToFile(cConversationName)
    try
        createConversationsDir()

        # إنشاء اسم ملف آمن
        cSafeFileName = ""
        for i = 1 to len(cConversationName)
            cChar = cConversationName[i]
            if isalnum(cChar) or cChar = " " or cChar = "-" or cChar = "_"
                cSafeFileName += cChar
            ok
        next

        if len(cSafeFileName) = 0
            cSafeFileName = "conversation"
        ok

        # إضافة التاريخ والوقت
        cTimeStamp = date() + "_" + time()
        cTimeStamp = substr(cTimeStamp, " ", "_")
        cTimeStamp = substr(cTimeStamp, ":", "-")

        cFileName = cConversationsDir + "/" + cSafeFileName + "_" + cTimeStamp + ".ring"

        # حفظ المحادثة
        cConversationData = list2code(aConversationHistory)
        write(cFileName, cConversationData)

        return "تم حفظ المحادثة: " + cSafeFileName
    catch
        return "خطأ في حفظ المحادثة: " + cCatchError
    done

# تحميل محادثة من ملف
func loadConversationFromFile(cFileName)
    try
        cFilePath = cConversationsDir + "/" + cFileName
        if fexists(cFilePath)
            cConversationData = read(cFilePath)
            cList = "aLoadedConversation = " + cConversationData
            eval(cList)
            return aLoadedConversation
        else
            return []
        ok
    catch
        return []
    done

# الحصول على قائمة المحادثات المحفوظة
func getConversationsList()
    try
        createConversationsDir()
        aConversations = []

        # قراءة ملفات المجلد
        aFiles = dir(cConversationsDir)
        for aFile in aFiles
            cFileName = aFile[1]
            if right(cFileName, 5) = ".ring"
                # استخراج اسم المحادثة من اسم الملف
                cDisplayName = left(cFileName, len(cFileName) - 5)
                cDisplayName = substr(cDisplayName, "_", " ")

                aConversations + [cFileName, cDisplayName]
            ok
        next

        return aConversations
    catch
        return []
    done

# حذف ملف محادثة
func deleteConversationFile(cFileName)
    try
        cFilePath = cConversationsDir + "/" + cFileName
        if fexists(cFilePath)
            remove(cFilePath)
            return "تم حذف المحادثة بنجاح"
        else
            return "الملف غير موجود"
        ok
    catch
        return "خطأ في حذف المحادثة: " + cCatchError
    done

# تحديث إعدادات API في الملف
func updateApiSettingsInFile(cNewApiKey, cNewApiUrl)
    try
        # البحث عن إعدادات API في القائمة أو إضافتها
        bApiKeyFound = false
        bApiUrlFound = false

        for i = 1 to len(aSettings)
            if aSettings[i][1] = "apiKey"
                aSettings[i][2] = cNewApiKey
                bApiKeyFound = true
            ok
            if aSettings[i][1] = "apiUrl"
                aSettings[i][2] = cNewApiUrl
                bApiUrlFound = true
            ok
        next

        # إضافة الإعدادات إذا لم تكن موجودة
        if not bApiKeyFound
            aSettings + ["apiKey", cNewApiKey]
        ok
        if not bApiUrlFound
            aSettings + ["apiUrl", cNewApiUrl]
        ok

        saveSettings()
    catch
        # تجاهل الأخطاء
    done

# ===================================================================
# الدوال المساعدة
# ===================================================================
func escapeJson(str)
    if str = NULL { return "" }
    escaped = ""
    for i = 1 to len(str) {
        char = str[i]
        if char = '"' { escaped += '\"' 
        elseif char = "\"  escaped += '\\' 
        elseif char = '/'  escaped += '\/' 
        elseif ascii(char) = 8  escaped += '\b' 
        elseif ascii(char) = 12  escaped += '\f' 
        elseif ascii(char) = 10  escaped += '\n' 
        elseif ascii(char) = 13  escaped += '\r' 
        elseif ascii(char) = 9  escaped += '\t' 
        else  escaped += char }
    }
    return escaped

func list2map(aList)
    oMap = []
    for item in aList { oMap[item[1]] = item[2] }
    return oMap

func iif(bCondition, vTrue, vFalse)
    if bCondition { return vTrue }
    return vFalse

func isalnum(cChar)
    nAscii = ascii(cChar)
    return (nAscii >= 48 and nAscii <= 57) or (nAscii >= 65 and nAscii <= 90) or (nAscii >= 97 and nAscii <= 122)
