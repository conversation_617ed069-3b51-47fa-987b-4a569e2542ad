aPackageInfo = [
	:name = "The chatBot Package",
	:description = "Our chatBot package using the Ring programming language",
	:folder = "chatBot",
	:developer = "Azzeddine Remmal",
	:email = "<EMAIL>",
	:license = "MIT License",
	:version = "1.0.0",
	:ringversion = "1.23",
	:versions = 	[
		[
			:version = "1.0.0",
			:branch = "master"
		]
	],
	:libs = 	[
		[
			:name = "webview.ring",
			:version = "1.3.4",
			:providerusername = "ysdragon"
		],
		[
			:name = "jsonlib.ring",
			:version = "1.0.16",
			:providerusername = "ringpackages"
		],
		[
			:name = "stdlib.ring",
			:version = "",
			:providerusername = "ringpackages"
	    ]
		
	],
	:files = 	[
		"lib.ring",
		"main.ring",
		"http_client.ring",
		"index.html"
	],
	:ringfolderfiles = 	[

	],
	:windowsfiles = 	[

	],
	:linuxfiles = 	[

	],
	:ubuntufiles = 	[

	],
	:fedorafiles = 	[

	],
	:freebsdfiles = 	[

	],
	:macosfiles = 	[

	],
	:windowsringfolderfiles = 	[

	],
	:linuxringfolderfiles = 	[

	],
	:ubunturingfolderfiles = 	[

	],
	:fedoraringfolderfiles = 	[

	],
	:freebsdringfolderfiles = 	[

	],
	:macosringfolderfiles = 	[

	],
	:run = "ring main.ring",
	:windowsrun = "",
	:linuxrun = "",
	:macosrun = "",
	:ubunturun = "",
	:fedorarun = "",
	:setup = "",
	:windowssetup = "",
	:linuxsetup = "",
	:macossetup = "",
	:ubuntusetup = "",
	:fedorasetup = "",
	:remove = "",
	:windowsremove = "",
	:linuxremove = "",
	:macosremove = "",
	:ubunturemove = "",
	:fedoraremove = ""
]